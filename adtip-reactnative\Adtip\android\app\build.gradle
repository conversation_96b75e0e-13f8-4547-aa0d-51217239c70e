apply plugin: "com.android.application"
// apply plugin: 'com.google.gms.google-services' // Moved to the end
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    hermesFlags = ["-O", "-output-source-map"]

    /* Production Bundle Optimization */
    bundleCommand = "bundle"
    extraPackagerArgs = [
        //"--minify",
        "--reset-cache"
    ]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * Enable R8 full mode with safer optimization rules
 * Uses safer configuration in proguard-rules-r8.pro to avoid unstable optimizations
 */
def enableR8FullMode = false

/**
 * Enable bundle splitting by ABI to reduce APK size
 */
def enableSeparateBuildPerCPUArchitecture = false



/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.adtip.app.adtip_app"
    defaultConfig {
        applicationId "com.adtip.app.adtip_app"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 30004
        versionName "33.0.0"

        // Enable multidex for large apps
        multiDexEnabled true

        // Optimize for production
        resConfigs "en", "xxhdpi"

        // Enable vector drawable support
        vectorDrawables.useSupportLibrary = true

        // Proguard optimization
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file('adtip-jks1.jks')
            storePassword 'adtip@123'
            keyAlias 'adtip'
            keyPassword 'adtip@123'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            // applicationIdSuffix ".debug"  // Commented out to fix React Native CLI launch issue
            debuggable true
            minifyEnabled false
            shrinkResources false
        }
        release {
            // Production release configuration
            signingConfig signingConfigs.release
            //minifyEnabled enableProguardInReleaseBuilds
            //shrinkResources enableProguardInReleaseBuilds
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true

            // Use optimized ProGuard configuration
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"

            // Enable R8 full mode
            if (enableR8FullMode) {
                proguardFiles 'proguard-rules-r8.pro'
            }
        }
    }

    // Split APKs by ABI for smaller download sizes (ARM only - real devices)
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // Set to true if you want a universal APK alongside ABI-specific ones
            include "armeabi-v7a", "arm64-v8a"
        }
    }

    // Map version codes for ABI splits (ARM only)
    project.ext.abiCodes = [
        'armeabi-v7a': 1,
        'arm64-v8a': 2
    ]

    // Automatically assign version codes for ABI splits
    android.applicationVariants.all { variant ->
        variant.outputs.each { output ->
            def abiVersionCode = project.ext.abiCodes.get(output.getFilter(com.android.build.OutputFile.ABI))
            if (abiVersionCode != null) {
                output.versionCodeOverride = defaultConfig.versionCode * 10 + abiVersionCode
            }
        }
    }

    // Optimize packaging
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        pickFirst '**/libhermes.so'
        pickFirst '**/libreactnativejni.so'
        pickFirst '**/libfbjni.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/*.kotlin_module'
        exclude '**/kotlin/**'
        exclude '**/*.kotlin_metadata'
        exclude '**/*.kotlin_builtins'
        exclude '**/kotlin-tooling-metadata.json'
    }

    // Compile options for optimization
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    // Bundle configuration for AAB optimization
    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    
    // PubScale Offerwall SDK
    implementation 'com.pubscale.sdkone:offerwall:1.0.11'

    // Firebase BoM - Recommended
    implementation platform('com.google.firebase:firebase-bom:33.15.0') // Or the latest version

    // Firebase Analytics - Recommended for most Firebase setups
    implementation 'com.google.firebase:firebase-analytics'

    // Firebase Messaging (version managed by BoM)
    implementation 'com.google.firebase:firebase-messaging' 
    // implementation 'com.google.firebase:firebase-messaging:23.1.2' // Remove specific version
    
    // VideoSDK Dependencies
    //implementation project(':rnwebrtc')        // MISSING - ADDED
    implementation project(':rnincallmanager')    // MISSING - ADDED

    implementation project(':react-native-callkeep')

    // ADD EXOPLAYER DEPENDENCIES - REQUIRED FOR ENHANCED TIPSHORTS
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-core:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-hls:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-dash:2.19.1'

    implementation project(':react-native-fs')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")

// Apply the Google services plugin at the very end of the file
apply plugin: 'com.google.gms.google-services'

