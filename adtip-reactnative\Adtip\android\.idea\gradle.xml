<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" name="gradle-plugin-root">
                <projects>
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/react-native-gradle-plugin" />
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/settings-plugin" />
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/shared" />
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/shared-testutil" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/../node_modules/@d11/react-native-fast-image/android" />
            <option value="$PROJECT_DIR$/../node_modules/@notifee/react-native/android" />
            <option value="$PROJECT_DIR$/../node_modules/@nozbe/watermelondb/native/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-async-storage/async-storage/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-clipboard/clipboard/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/blur/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/datetimepicker/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/masked-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/netinfo/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/progress-bar-android/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/analytics/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/app/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/auth/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/crashlytics/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/database/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/firestore/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/messaging/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/storage/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/react-native-gradle-plugin" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/settings-plugin" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/shared" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin/shared-testutil" />
            <option value="$PROJECT_DIR$/../node_modules/@shopify/react-native-skia/android" />
            <option value="$PROJECT_DIR$/../node_modules/@videosdk.live/react-native-incallmanager/android" />
            <option value="$PROJECT_DIR$/../node_modules/@videosdk.live/react-native-webrtc/android" />
            <option value="$PROJECT_DIR$/../node_modules/lottie-react-native/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-callkeep/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-compressor/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-create-thumbnail/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-date-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-fs/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-geolocation-service/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-gesture-handler/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-get-random-values/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-google-mobile-ads/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-image-crop-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-image-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-linear-gradient/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-orientation-locker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-pager-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-permissions/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-razorpay/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-reanimated/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-restart/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-safe-area-context/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-screens/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-svg/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-vector-icons/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-video/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-view-shot/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-vision-camera/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-webview/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>