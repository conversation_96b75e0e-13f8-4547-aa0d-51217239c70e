{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-e9d47f854dff6001990b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-7a2edec953087a90a09c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1d7a8f34f48059a5d93d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-7a2edec953087a90a09c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-1d7a8f34f48059a5d93d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e9d47f854dff6001990b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}