{"artifacts": [{"path": "F:/A1/adtip-reactnative/Adtip/android/app/build/intermediates/cxx/Debug/4i5e4669/obj/armeabi-v7a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 83, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 81, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 89, "parent": 12}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "F:/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 11, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 13, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot"}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 5, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_rnclipboard::@6385240493dfcaf22ab7"}, {"backtrace": 4, "id": "react_codegen_RNFastImageSpec::@5f53d33017f3c907f455"}, {"backtrace": 4, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2"}, {"backtrace": 4, "id": "react_codegen_rnskia::@376d8504f62839611b97"}, {"backtrace": 4, "id": "react_codegen_rnblurview::@9a34ffec6e39b5c9049e"}, {"backtrace": 4, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb"}, {"backtrace": 4, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c"}, {"backtrace": 4, "id": "react_codegen_Compressor::@408161e29a6d5b274579"}, {"backtrace": 4, "id": "react_codegen_RNDatePickerSpecs::@c00f981c6e74346c63d4"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_RNCImageCropPickerSpec::@702b02f8d2524609d414"}, {"backtrace": 4, "id": "react_codegen_pagerview::@7032a8921530ec438d60"}, {"backtrace": 4, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957"}, {"backtrace": 4, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}, {"backtrace": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}, {"backtrace": 4, "id": "react_codegen_rnviewshot::@0ba03d237e60b9258a87"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 4, "fragment": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "role": "libraries"}, {"backtrace": 5, "fragment": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/RNFastImageSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/RNFastImageSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNFastImageSpec_autolinked_build/CMakeFiles/react_codegen_RNFastImageSpec.dir/react/renderer/components/RNFastImageSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/react/renderer/components/rnblurview/rnblurviewJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnblurview_autolinked_build/CMakeFiles/react_codegen_rnblurview.dir/rnblurview-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/rnskiaJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/rnskia-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/Compressor-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/CompressorJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/Compressor_autolinked_build/CMakeFiles/react_codegen_Compressor.dir/react/renderer/components/Compressor/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/RNDatePickerSpecs-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/RNDatePickerSpecsJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNDatePickerSpecs_autolinked_build/CMakeFiles/react_codegen_RNDatePickerSpecs.dir/react/renderer/components/RNDatePickerSpecs/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/RNCImageCropPickerSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/c7be1e84762439d6b46bf87a234db436/RNCImageCropPickerSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/rnviewshotJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/rnviewshot-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}