# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 155ms
  generate-prefab-packages
    [gap of 45ms]
    exec-prefab 790ms
    [gap of 63ms]
  generate-prefab-packages completed in 898ms
  execute-generate-process
    [gap of 27ms]
    exec-configure 2543ms
    [gap of 385ms]
  execute-generate-process completed in 2955ms
  [gap of 47ms]
  remove-unexpected-so-files 65ms
  [gap of 68ms]
  write-metadata-json-to-file 13ms
  [gap of 20ms]
generate_cxx_metadata completed in 4290ms

