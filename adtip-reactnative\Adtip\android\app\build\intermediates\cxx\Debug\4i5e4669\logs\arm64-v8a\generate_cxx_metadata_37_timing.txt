# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 653ms
  generate-prefab-packages
    [gap of 75ms]
    exec-prefab 889ms
    [gap of 15ms]
  generate-prefab-packages completed in 979ms
  execute-generate-process
    exec-configure 1164ms
    [gap of 404ms]
  execute-generate-process completed in 1568ms
  remove-unexpected-so-files 32ms
  [gap of 48ms]
generate_cxx_metadata completed in 3316ms

