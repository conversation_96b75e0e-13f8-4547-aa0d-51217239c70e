{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6564", "endColumns": "129", "endOffsets": "6689"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "88,162,163,164", "startColumns": "4,4,4,4", "startOffsets": "7742,13557,13657,13770", "endColumns": "110,99,112,97", "endOffsets": "7848,13652,13765,13863"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,244,319,412,504,602,698,787,894,983,1048,1153,1244,1338,1417,1513,1599,1695,1766,1833,1915,2001,2098", "endColumns": "76,111,74,92,91,97,95,88,106,88,64,104,90,93,78,95,85,95,70,66,81,85,96,101", "endOffsets": "127,239,314,407,499,597,693,782,889,978,1043,1148,1239,1333,1412,1508,1594,1690,1761,1828,1910,1996,2093,2195"}, "to": {"startLines": "89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7853,8059,8171,8246,8339,8431,8529,8625,8714,8821,8910,8975,9080,9171,9265,9344,9440,9526,14954,15025,15092,15174,15260,15357", "endColumns": "76,111,74,92,91,97,95,88,106,88,64,104,90,93,78,95,85,95,70,66,81,85,96,101", "endOffsets": "7925,8166,8241,8334,8426,8524,8620,8709,8816,8905,8970,9075,9166,9260,9339,9435,9521,9617,15020,15087,15169,15255,15352,15454"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1136,1243,1344,1450,1536,1640,1762,1847,1929,2020,2113,2208,2302,2402,2495,2590,2695,2786,2877,2963,3068,3174,3277,3384,3493,3600,3770,20800", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1238,1339,1445,1531,1635,1757,1842,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3379,3488,3595,3765,3862,20882"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5567,5674,5832,5959,6069,6223,6350,6462,6694,6843,6950,7110,7237,7386,7529,7597,7662", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "5669,5827,5954,6064,6218,6345,6457,6559,6838,6945,7105,7232,7381,7524,7592,7657,7737"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11602,11677,11738,11803,11875,11954,12027,12115,12199", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "11672,11733,11798,11870,11949,12022,12110,12194,12269"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "206,250,297,353,418,486,598,661,790,898,1015,1070,1127,1232,1318,1359,1449,1485,1518,1577,1665,1705", "endColumns": "43,46,55,64,67,111,62,128,107,116,54,56,104,85,40,89,35,32,58,87,39,55", "endOffsets": "249,296,352,417,485,597,660,789,897,1014,1069,1126,1231,1317,1358,1448,1484,1517,1576,1664,1704,1760"}, "to": {"startLines": "231,232,233,234,235,236,237,238,239,240,241,242,243,244,246,247,248,249,250,251,252,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19137,19185,19236,19296,19365,19437,19553,19620,19753,19865,19986,20045,20106,20215,20385,20430,20524,20564,20601,20664,20756,21224", "endColumns": "47,50,59,68,71,115,66,132,111,120,58,60,108,89,44,93,39,36,62,91,43,59", "endOffsets": "19180,19231,19291,19360,19432,19548,19615,19748,19860,19981,20040,20101,20210,20300,20425,20519,20559,20596,20659,20751,20795,21279"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1130,1196,1289,1357,1420,1523,1583,1649,1705,1776,1836,1890,2002,2059,2120,2174,2250,2375,2462,2539,2632,2716,2799,2938,3020,3103,3234,3322,3400,3454,3510,3576,3650,3728,3799,3881,3957,4033,4108,4180,4287,4377,4450,4542,4638,4710,4786,4882,4935,5017,5084,5171,5258,5320,5384,5447,5516,5621,5731,5827,5935,5993,6053,6133,6216,6292", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "319,396,473,553,661,755,849,981,1062,1125,1191,1284,1352,1415,1518,1578,1644,1700,1771,1831,1885,1997,2054,2115,2169,2245,2370,2457,2534,2627,2711,2794,2933,3015,3098,3229,3317,3395,3449,3505,3571,3645,3723,3794,3876,3952,4028,4103,4175,4282,4372,4445,4537,4633,4705,4781,4877,4930,5012,5079,5166,5253,5315,5379,5442,5511,5616,5726,5822,5930,5988,6048,6128,6211,6287,6364"}, "to": {"startLines": "21,55,56,57,58,59,67,68,69,90,91,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,245,254,255,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,4098,4175,4252,4332,4440,5260,5354,5486,7930,7993,13464,13868,13936,13999,14102,14162,14228,14284,14355,14415,14469,14581,14638,14699,14753,14829,15459,15546,15623,15716,15800,15883,16022,16104,16187,16318,16406,16484,16538,16594,16660,16734,16812,16883,16965,17041,17117,17192,17264,17371,17461,17534,17626,17722,17794,17870,17966,18019,18101,18168,18255,18342,18404,18468,18531,18600,18705,18815,18911,19019,19077,20305,20887,20970,21046", "endLines": "25,55,56,57,58,59,67,68,69,90,91,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,245,254,255,256", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "1131,4170,4247,4327,4435,4529,5349,5481,5562,7988,8054,13552,13931,13994,14097,14157,14223,14279,14350,14410,14464,14576,14633,14694,14748,14824,14949,15541,15618,15711,15795,15878,16017,16099,16182,16313,16401,16479,16533,16589,16655,16729,16807,16878,16960,17036,17112,17187,17259,17366,17456,17529,17621,17717,17789,17865,17961,18014,18096,18163,18250,18337,18399,18463,18526,18595,18700,18810,18906,19014,19072,19132,20380,20965,21041,21118"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "60,61,62,63,64,65,66,257", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4534,4632,4734,4831,4935,5039,5144,21123", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4627,4729,4826,4930,5034,5139,5255,21219"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3518,3580,3656,3732,3788", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3513,3575,3651,3727,3783,3851"}, "to": {"startLines": "2,11,16,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,9622,9704,9787,9869,9958,10049,10119,10186,10280,10375,10443,10507,10570,10642,10751,10865,10976,11052,11140,11214,11285,11377,11470,11537,12274,12327,12385,12433,12494,12560,12624,12687,12752,12816,12877,13074,13126,13188,13264,13340,13396", "endLines": "10,15,20,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "377,646,907,9699,9782,9864,9953,10044,10114,10181,10275,10370,10438,10502,10565,10637,10746,10860,10971,11047,11135,11209,11280,11372,11465,11532,11597,12322,12380,12428,12489,12555,12619,12682,12747,12811,12872,12938,13121,13183,13259,13335,13391,13459"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,119", "endOffsets": "161,281"}, "to": {"startLines": "53,54", "startColumns": "4,4", "startOffsets": "3867,3978", "endColumns": "110,119", "endOffsets": "3973,4093"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "3466,3531", "endColumns": "64,65", "endOffsets": "3526,3592"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "12943,13008", "endColumns": "64,65", "endOffsets": "13003,13069"}}]}]}